'use client'

import { ThemeController } from '@/components/ThemeController'
import { UserProfile } from '@/components/UserProfile'
import Link from 'next/link'
import Image from 'next/image'

export default function DashboardPage() {
  return (
    <div className="size-full flex">
      <div className="flex h-screen min-w-0 grow flex-col overflow-auto">
        {/* Notifications */}
        <input
          type="checkbox"
          id="layout-sidebar-toggle-trigger"
          aria-label="Toggle menu"
          className="hidden"
        />
        <div className="w-[var(--layout-sidebar-width)] min-w-[var(--layout-sidebar-width)] bg-[var(--layout-sidebar-background)] max-h-[calc(100vh-32px)] top-4 bottom-4 rounded-box transition-all duration-300 flex-col flex relative lg:z-[500] lg:fixed">
          <Link href="/" className="flex min-h-16 items-center justify-center">
            <Image src="/logo-light.svg" alt="logo" width={103} height={20} />
          </Link>
        </div>
        <label
          htmlFor="layout-sidebar-toggle-trigger"
          className="lg:transition-all lg:bg-base-content/5 lg:inset-0 lg:z-[499] lg:fixed"
        ></label>

        {/* NAVBAR */}
        <div
          role="navigation"
          aria-label="Navbar"
          className="flex items-center justify-between px-3 bg-[var(--layout-topbar-background)] z-50 h-16 static duration-300 transition-colors mx-5 mt-4 rounded-box"
        >
          <div className="inline-flex items-center gap-3">
            <label
              className="btn btn-square btn-ghost btn-sm"
              aria-label="Left menu toggle"
              htmlFor="layout-sidebar-toggle-trigger"
            >
              <span className="icon-[solar--hamburger-menu-line-duotone]"></span>
            </label>
            <button className="btn btn-outline btn-sm btn-ghost border-base-300 text-base-content/70 hidden h-9 w-48 justify-start gap-2 text-sm md:flex">
              <span className="icon-[solar--magnifer-outline] size-4"></span>
              <span>Search</span>
            </button>
          </div>
          <div className="inline-flex items-center gap-1.5">
            <ThemeController />
            {/* Notifications */}
            <UserProfile />
          </div>
        </div>
      </div>
    </div>
  )
}
